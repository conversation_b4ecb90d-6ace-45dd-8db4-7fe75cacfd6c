import React from 'react';
import { ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import {
  TestBeaker,
  Smartphone,
  LayoutList,
  Gauge,
  AlertCircle,
  Sparkles,
} from 'lucide-react-native';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { SafeAreaView } from '@/components/ui/safe-area-view';
import { Pressable } from '@/components/ui/pressable';

/**
 * Test index screen
 * This screen provides navigation to different test screens
 */
export default function TestIndex() {
  const { t } = useTranslation();
  const router = useRouter();

  // Test screens
  const testScreens = [
    {
      id: 'stories-screen',
      title: 'Stories Screen Test',
      description: 'Test the stories screen with different scenarios',
      icon: <LayoutList size={24} className="text-primary-500 dark:text-primary-400" />,
      route: '/test/stories-screen',
    },
    {
      id: 'device-compatibility',
      title: 'Device Compatibility Test',
      description: 'Test the app on different device sizes and orientations',
      icon: <Smartphone size={24} className="text-primary-500 dark:text-primary-400" />,
      route: '/test/device-compatibility',
    },
    {
      id: 'performance',
      title: 'Performance Test',
      description: 'Test the app with large datasets and measure performance',
      icon: <Gauge size={24} className="text-primary-500 dark:text-primary-400" />,
      route: '/test/performance',
    },
    {
      id: 'error-handling',
      title: 'Error Handling Test',
      description: 'Test the app with different error scenarios',
      icon: <AlertCircle size={24} className="text-primary-500 dark:text-primary-400" />,
      route: '/test/error-handling',
    },
    {
      id: 'animations',
      title: 'Animations Test',
      description: 'Test the app animations and transitions',
      icon: <Sparkles size={24} className="text-primary-500 dark:text-primary-400" />,
      route: '/test/animations',
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-background-50 dark:bg-background-900" edges={['top', 'left', 'right']}>
      <ScrollView>
        <Box className="p-4 bg-background-100 dark:bg-background-800 border-b border-outline-200 dark:border-outline-700">
          <Text className="text-2xl font-bold text-typography-900 dark:text-typography-100 mb-2">
            Test Suite
          </Text>
          <Text className="text-base text-typography-600 dark:text-typography-400 mb-4">
            Select a test to run and verify the app functionality
          </Text>

          <Button
            className="mt-4 p-4 bg-primary-500 dark:bg-primary-600 rounded-lg items-center"
            onPress={() => router.push('/')}
          >
            <Text className="text-typography-950 dark:text-typography-50 font-bold">
              Back to App
            </Text>
          </Button>
        </Box>

        <Box className="p-4">
          {testScreens.map((screen) => (
            <Pressable
              key={screen.id}
              className="bg-background-50 dark:bg-background-800 rounded-lg p-4 mb-4 border border-outline-200 dark:border-outline-700 flex-row items-center"
              onPress={() => router.push(screen.route)}
            >
              <Box className="mr-4">{screen.icon}</Box>
              <Box className="flex-1">
                <Text className="text-lg font-bold text-typography-900 dark:text-typography-100 mb-1">
                  {screen.title}
                </Text>
                <Text className="text-sm text-typography-600 dark:text-typography-400">
                  {screen.description}
                </Text>
              </Box>
            </Pressable>
          ))}
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
}
