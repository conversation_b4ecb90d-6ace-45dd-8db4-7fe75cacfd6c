import React from 'react';
import { Animated } from 'react-native';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';

interface AnimationPreviewProps {
  fadeAnim: Animated.Value;
  slideAnim: Animated.Value;
  spin: Animated.AnimatedInterpolation<string | number>;
  scaleAnim: Animated.Value;
}

export default function AnimationPreview({
  fadeAnim,
  slideAnim,
  spin,
  scaleAnim,
}: AnimationPreviewProps) {
  return (
    <Box className="items-center justify-center p-4 bg-primary-50 dark:bg-primary-900 rounded-lg mb-4 h-[100px]">
      <Animated.View
        style={{
          opacity: fadeAnim,
          transform: [
            { translateX: slideAnim },
            { rotate: spin },
            { scale: scaleAnim },
          ],
        }}
      >
        <Text className="text-base font-bold text-primary-700 dark:text-primary-300">
          Animation Preview
        </Text>
      </Animated.View>
    </Box>
  );
}
