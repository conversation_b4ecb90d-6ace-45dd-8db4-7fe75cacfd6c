import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';

interface AnimationType {
  id: string;
  title: string;
  description: string;
  run: () => void;
}

interface AnimationCardProps {
  animation: AnimationType;
}

export default function AnimationCard({ animation }: AnimationCardProps) {
  return (
    <Box 
      key={animation.id} 
      className="bg-background-50 dark:bg-background-800 rounded-lg p-4 mb-2 border border-outline-200 dark:border-outline-700 flex-row items-center"
    >
      <Box className="flex-1">
        <Text className="text-base font-bold text-typography-900 dark:text-typography-100 mb-1">
          {animation.title}
        </Text>
        <Text className="text-sm text-typography-600 dark:text-typography-400">
          {animation.description}
        </Text>
      </Box>
      
      <Button
        className="bg-primary-500 dark:bg-primary-600 px-4 py-2 rounded-lg"
        onPress={animation.run}
      >
        <Text className="text-typography-950 dark:text-typography-50 font-bold">
          Run
        </Text>
      </Button>
    </Box>
  );
}
