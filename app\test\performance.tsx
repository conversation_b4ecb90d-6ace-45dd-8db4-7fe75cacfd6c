import React from 'react';
import { StoryTabs } from '@/features/stories/components/story-tabs';
import { StoryList } from '@/features/stories/components/story-list';
import { usePerformanceTest } from '@/features/test/hooks/use-performance-test';
import PerformanceMetrics from '@/features/test/components/performance-metrics';
import { Box } from '@/components/ui/box';
import { SafeAreaView } from '@/components/ui/safe-area-view';

/**
 * Performance test screen
 * This screen tests the performance of the app with different data set sizes
 */
export default function PerformanceTest() {
  // Use the performance test hook
  const {
    activeTab,
    setActiveTab,
    stories,
    isLoading,
    isRefreshing,
    dataSetSize,
    setDataSetSize,
    renderTime,
    memoryUsage,
    dataSizes,
    handleRefresh,
    handleStoryPress,
  } = usePerformanceTest();

  return (
    <SafeAreaView
      className="flex-1 bg-background-50 dark:bg-background-900"
      edges={['top', 'left', 'right']}
    >
      <PerformanceMetrics
        dataSetSize={dataSetSize}
        renderTime={renderTime}
        memoryUsage={memoryUsage}
        dataSizes={dataSizes}
        onDataSizeChange={setDataSetSize}
      />

      <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />

      <Box className="flex-1">
        <StoryList
          stories={stories}
          onStoryPress={handleStoryPress}
          isLoading={isLoading}
          isRefreshing={isRefreshing}
          onRefresh={handleRefresh}
          hasMoreStories={false}
        />
      </Box>
    </SafeAreaView>
  );
}
