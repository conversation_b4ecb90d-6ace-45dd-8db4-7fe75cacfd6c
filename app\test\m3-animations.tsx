import React, { useState } from 'react';
import { View, ScrollView, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/lib/theme/theme-provider';
import {
  M3HeadlineMedium,
  M3TitleMedium,
  M3BodyMedium,
  M3LabelLarge,
} from '@/components/ui/text/material3-text';
import M3ExpressiveButton from '@/components/ui/button/material3-expressive-button';
import {
  useM3ButtonAnimation,
  useM3FadeAnimation,
  useM3ScaleAnimation,
  useM3SlideAnimation,
  useM3ShimmerAnimation,
  useM3ElasticAnimation,
  useM3CompoundAnimation,
} from '@/hooks/use-m3-animation';
import { M3AnimationUtils } from '@/lib/animation/material3-expressive-animations';
import { MaterialSymbol } from '@/lib/icons/material-symbols';

export default function M3AnimationsTestPage() {
  const { themeColors, toggleTheme, currentTheme } = useTheme();
  const [showDemoCard, setShowDemoCard] = useState(false);

  // 各种动画示例
  const bounceAnimation = useM3ButtonAnimation({ initialValue: 1 });
  const fadeAnimation = useM3FadeAnimation({ initialValue: 0 });
  const scaleAnimation = useM3ScaleAnimation({ initialValue: 1 });
  const slideAnimation = useM3SlideAnimation('up', { initialValue: 0 });
  const shimmerAnimation = useM3ShimmerAnimation({ initialValue: 0.3 });
  const elasticAnimation = useM3ElasticAnimation({ initialValue: 1 });

  // 复合动画示例
  const compoundAnimation = useM3CompoundAnimation([
    { type: 'scale', options: { initialValue: 1 } },
    { type: 'fadeIn', options: { initialValue: 0 } },
    { type: 'slideUp', options: { initialValue: 0 } },
  ]);

  const handleShowCard = () => {
    setShowDemoCard(true);
    fadeAnimation.start();
    slideAnimation.start();
  };

  const handleHideCard = () => {
    setShowDemoCard(false);
    fadeAnimation.reset(0);
    slideAnimation.reset(0);
  };

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: themeColors.background.main }}
    >
      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 20 }}>
        {/* 页面标题 */}
        <View style={{ marginBottom: 32 }}>
          <M3HeadlineMedium
            style={{ color: themeColors.primary.main, marginBottom: 8 }}
          >
            Material 3 Expressive 动画系统
          </M3HeadlineMedium>
          <M3BodyMedium style={{ color: themeColors.background.on }}>
            体验富有表现力的动画效果和交互反馈
          </M3BodyMedium>
        </View>

        {/* 主题切换 */}
        <View
          style={{
            backgroundColor: themeColors.surface.container,
            borderRadius: 16,
            padding: 16,
            marginBottom: 24,
          }}
        >
          <M3TitleMedium
            style={{ color: themeColors.background.on, marginBottom: 12 }}
          >
            主题切换动画
          </M3TitleMedium>
          <M3ExpressiveButton
            variant="filled"
            icon="palette"
            onPress={toggleTheme}
            enableAnimations={true}
            enableHaptics={true}
          >
            切换到 {currentTheme === 'light' ? '深色' : '浅色'} 主题
          </M3ExpressiveButton>
        </View>

        {/* 按钮动画展示 */}
        <View
          style={{
            backgroundColor: themeColors.surface.container,
            borderRadius: 16,
            padding: 16,
            marginBottom: 24,
          }}
        >
          <M3TitleMedium
            style={{ color: themeColors.background.on, marginBottom: 12 }}
          >
            按钮动画变体
          </M3TitleMedium>

          <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 12 }}>
            <M3ExpressiveButton
              variant="filled"
              size="small"
              icon="play_arrow"
              onPress={() => bounceAnimation.start()}
            >
              Filled
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="filled-tonal"
              size="medium"
              icon="favorite"
              iconPosition="right"
              onPress={() => scaleAnimation.start()}
            >
              Filled Tonal
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="outlined"
              size="large"
              icon="star"
              onPress={() => elasticAnimation.start()}
            >
              Outlined
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="text"
              icon="share"
              onPress={() => compoundAnimation.startAll()}
            >
              Text Button
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="elevated"
              icon="cloud_upload"
              loading={false}
              onPress={() => shimmerAnimation.start()}
            >
              Elevated
            </M3ExpressiveButton>
          </View>
        </View>

        {/* 动画演示区域 */}
        <View
          style={{
            backgroundColor: themeColors.surface.container,
            borderRadius: 16,
            padding: 16,
            marginBottom: 24,
          }}
        >
          <M3TitleMedium
            style={{ color: themeColors.background.on, marginBottom: 12 }}
          >
            动画演示
          </M3TitleMedium>

          <View style={{ flexDirection: 'row', gap: 12, marginBottom: 16 }}>
            <M3ExpressiveButton
              variant="filled"
              size="small"
              onPress={handleShowCard}
            >
              显示卡片
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="outlined"
              size="small"
              onPress={handleHideCard}
            >
              隐藏卡片
            </M3ExpressiveButton>
          </View>

          {/* 动画卡片 */}
          {showDemoCard && (
            <Animated.View
              style={{
                backgroundColor: themeColors.tertiary.container,
                borderRadius: 12,
                padding: 16,
                marginTop: 16,
                opacity: fadeAnimation.value,
                transform: [
                  {
                    translateY: M3AnimationUtils.interpolate(
                      slideAnimation.value,
                      [0, 1],
                      [50, 0]
                    ),
                  },
                  {
                    scale: M3AnimationUtils.interpolate(
                      compoundAnimation.animations[0].value,
                      [0, 1],
                      [0.8, 1]
                    ),
                  },
                ],
              }}
            >
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  marginBottom: 8,
                }}
              >
                <MaterialSymbol
                  name="animation"
                  size="medium"
                  color={themeColors.tertiary.onContainer}
                  style={{ marginRight: 8 }}
                />
                <M3TitleMedium
                  style={{ color: themeColors.tertiary.onContainer }}
                >
                  动画卡片
                </M3TitleMedium>
              </View>
              <M3BodyMedium style={{ color: themeColors.tertiary.onContainer }}>
                这是一个使用 Material 3 Expressive 动画系统的示例卡片。
                它展示了淡入、滑动和缩放动画的组合效果。
              </M3BodyMedium>
            </Animated.View>
          )}
        </View>

        {/* 交互式动画元素 */}
        <View
          style={{
            backgroundColor: themeColors.surface.container,
            borderRadius: 16,
            padding: 16,
            marginBottom: 24,
          }}
        >
          <M3TitleMedium
            style={{ color: themeColors.background.on, marginBottom: 12 }}
          >
            交互式动画元素
          </M3TitleMedium>

          {/* 弹性缩放示例 */}
          <Animated.View
            style={{
              transform: [
                {
                  scale: M3AnimationUtils.interpolate(
                    elasticAnimation.value,
                    [0, 1],
                    [1, 1.1]
                  ),
                },
              ],
            }}
          >
            <View
              style={{
                backgroundColor: themeColors.primary.container,
                borderRadius: 12,
                padding: 16,
                marginBottom: 12,
                alignItems: 'center',
              }}
            >
              <MaterialSymbol
                name="gesture"
                size="large"
                color={themeColors.primary.onContainer}
                style={{ marginBottom: 8 }}
              />
              <M3LabelLarge style={{ color: themeColors.primary.onContainer }}>
                点击上方按钮查看弹性效果
              </M3LabelLarge>
            </View>
          </Animated.View>

          {/* 微光效果示例 */}
          <Animated.View
            style={{
              opacity: M3AnimationUtils.interpolate(
                shimmerAnimation.value,
                [0, 1],
                [0.3, 1]
              ),
            }}
          >
            <View
              style={{
                backgroundColor: themeColors.secondary.container,
                borderRadius: 12,
                padding: 16,
                alignItems: 'center',
              }}
            >
              <MaterialSymbol
                name="shimmer"
                size="large"
                color={themeColors.secondary.onContainer}
                style={{ marginBottom: 8 }}
              />
              <M3LabelLarge
                style={{ color: themeColors.secondary.onContainer }}
              >
                微光动画效果演示
              </M3LabelLarge>
            </View>
          </Animated.View>
        </View>

        {/* 动画控制面板 */}
        <View
          style={{
            backgroundColor: themeColors.surface.container,
            borderRadius: 16,
            padding: 16,
            marginBottom: 24,
          }}
        >
          <M3TitleMedium
            style={{ color: themeColors.background.on, marginBottom: 12 }}
          >
            动画控制
          </M3TitleMedium>

          <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
            <M3ExpressiveButton
              variant="outlined"
              size="small"
              icon="play_arrow"
              onPress={() => bounceAnimation.start()}
            >
              弹跳
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="outlined"
              size="small"
              icon="visibility"
              onPress={() => fadeAnimation.start()}
            >
              淡入
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="outlined"
              size="small"
              icon="zoom_in"
              onPress={() => scaleAnimation.start()}
            >
              缩放
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="outlined"
              size="small"
              icon="arrow_upward"
              onPress={() => slideAnimation.start()}
            >
              滑动
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="outlined"
              size="small"
              icon="auto_awesome"
              onPress={() => shimmerAnimation.start()}
            >
              微光
            </M3ExpressiveButton>

            <M3ExpressiveButton
              variant="outlined"
              size="small"
              icon="rubber_band"
              onPress={() => elasticAnimation.start()}
            >
              弹性
            </M3ExpressiveButton>
          </View>
        </View>

        {/* 重置所有动画 */}
        <M3ExpressiveButton
          variant="filled-tonal"
          icon="refresh"
          fullWidth
          onPress={() => {
            bounceAnimation.reset();
            fadeAnimation.reset();
            scaleAnimation.reset();
            slideAnimation.reset();
            elasticAnimation.reset();
            compoundAnimation.resetAll();
          }}
        >
          重置所有动画
        </M3ExpressiveButton>
      </ScrollView>
    </SafeAreaView>
  );
}
