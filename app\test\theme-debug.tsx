import React from 'react';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/lib/theme/theme-provider';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/m3e-button';

export default function ThemeDebugPage() {
  const { themeColors, currentTheme, toggleTheme, colors } = useTheme();

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: themeColors?.background?.main || '#FFFFFF',
      }}
    >
      <View style={{ padding: 20 }}>
        <Text
          style={{
            fontSize: 24,
            marginBottom: 20,
            color: themeColors?.primary?.main || '#000000',
          }}
        >
          主题调试页面
        </Text>

        <Text
          style={{
            marginBottom: 10,
            color: themeColors?.background?.on || '#000000',
          }}
        >
          当前主题: {currentTheme}
        </Text>

        <Text
          style={{
            marginBottom: 10,
            color: themeColors?.background?.on || '#000000',
          }}
        >
          新 API - Primary Color: {themeColors?.primary?.main || '未定义'}
        </Text>

        <Text
          style={{
            marginBottom: 10,
            color: themeColors?.background?.on || '#000000',
          }}
        >
          旧 API - Primary Color: {colors?.primary || '未定义'}
        </Text>

        <Button onPress={toggleTheme} style={{ marginTop: 20 }}>
          <ButtonText>切换主题</ButtonText>
        </Button>

        <View style={{ marginTop: 20 }}>
          <Text style={{ color: themeColors?.background?.on || '#000000' }}>
            主题颜色预览:
          </Text>

          <View
            style={{ flexDirection: 'row', flexWrap: 'wrap', marginTop: 10 }}
          >
            <View
              style={{
                width: 50,
                height: 50,
                backgroundColor: themeColors?.primary?.main,
                margin: 5,
                borderRadius: 4,
              }}
            />
            <View
              style={{
                width: 50,
                height: 50,
                backgroundColor: themeColors?.secondary?.main,
                margin: 5,
                borderRadius: 4,
              }}
            />
            <View
              style={{
                width: 50,
                height: 50,
                backgroundColor: themeColors?.tertiary?.main,
                margin: 5,
                borderRadius: 4,
              }}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}
