import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { SafeAreaView } from '@/components/ui/safe-area-view';
import { Text } from '@/components/ui/text';
import { Heading } from '@/components/ui/heading';
import { Box } from '@/components/ui/box';
import { M3Button } from '@/components/ui/m3e-button';
import { useTheme } from '@/lib/theme/theme-provider';

export default function M3ButtonsDemo() {
  const { themeColors, toggleTheme, isDark } = useTheme();
  const [loadingStates, setLoadingStates] = useState<{
    [key: string]: boolean;
  }>({});

  const handleButtonPress = (buttonId: string) => {
    setLoadingStates((prev) => ({ ...prev, [buttonId]: true }));
    setTimeout(() => {
      setLoadingStates((prev) => ({ ...prev, [buttonId]: false }));
    }, 2000);
  };

  const ButtonSection = ({
    title,
    children,
  }: {
    title: string;
    children: React.ReactNode;
  }) => (
    <Box className="mb-8">
      <Heading size="lg" className="text-gray-900 dark:text-white mb-4">
        {title}
      </Heading>
      <Box className="gap-4">{children}</Box>
    </Box>
  );

  const ButtonRow = ({
    label,
    children,
  }: {
    label: string;
    children: React.ReactNode;
  }) => (
    <Box className="mb-4">
      <Text
        size="md"
        className="text-gray-700 dark:text-gray-300 mb-2 font-medium"
      >
        {label}
      </Text>
      <View className="flex-row flex-wrap gap-3">{children}</View>
    </Box>
  );

  return (
    <SafeAreaView className="flex-1 bg-white dark:bg-gray-900">
      <ScrollView
        contentContainerStyle={{ padding: 20 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Box className="mb-6">
          <Heading size="2xl" className="text-gray-900 dark:text-white mb-2">
            Material Design 3 按钮
          </Heading>
          <Text size="md" className="text-gray-600 dark:text-gray-400 mb-4">
            符合 M3 Expressive 设计规范的按钮组件演示
          </Text>

          {/* Theme Toggle */}
          <M3Button
            variant="outlined"
            size="small"
            icon={isDark ? 'light_mode' : 'dark_mode'}
            onPress={toggleTheme}
          >
            切换主题
          </M3Button>
        </Box>

        {/* 基础按钮变体 */}
        <ButtonSection title="按钮变体 (Button Variants)">
          <ButtonRow label="Filled Buttons - 主要操作">
            <M3Button
              variant="filled"
              size="medium"
              onPress={() => handleButtonPress('filled-1')}
              loading={loadingStates['filled-1']}
            >
              Filled Button
            </M3Button>
            <M3Button
              variant="filled"
              size="medium"
              icon="favorite"
              onPress={() => handleButtonPress('filled-2')}
            >
              With Icon
            </M3Button>
            <M3Button variant="filled" size="medium" disabled>
              Disabled
            </M3Button>
          </ButtonRow>

          <ButtonRow label="Filled Tonal Buttons - 次要操作">
            <M3Button
              variant="tonal"
              size="medium"
              onPress={() => handleButtonPress('tonal-1')}
              loading={loadingStates['tonal-1']}
            >
              Tonal Button
            </M3Button>
            <M3Button
              variant="tonal"
              size="medium"
              icon="edit"
              onPress={() => handleButtonPress('tonal-2')}
            >
              With Icon
            </M3Button>
            <M3Button variant="tonal" size="medium" disabled>
              Disabled
            </M3Button>
          </ButtonRow>

          <ButtonRow label="Outlined Buttons - 中等重要性">
            <M3Button
              variant="outlined"
              size="medium"
              onPress={() => handleButtonPress('outlined-1')}
              loading={loadingStates['outlined-1']}
            >
              Outlined
            </M3Button>
            <M3Button
              variant="outlined"
              size="medium"
              icon="download"
              onPress={() => handleButtonPress('outlined-2')}
            >
              With Icon
            </M3Button>
            <M3Button variant="outlined" size="medium" disabled>
              Disabled
            </M3Button>
          </ButtonRow>

          <ButtonRow label="Text Buttons - 最低重要性">
            <M3Button
              variant="text"
              size="medium"
              onPress={() => handleButtonPress('text-1')}
              loading={loadingStates['text-1']}
            >
              Text Button
            </M3Button>
            <M3Button
              variant="text"
              size="medium"
              icon="share"
              onPress={() => handleButtonPress('text-2')}
            >
              With Icon
            </M3Button>
            <M3Button variant="text" size="medium" disabled>
              Disabled
            </M3Button>
          </ButtonRow>

          <ButtonRow label="Elevated Buttons - 悬浮效果">
            <M3Button
              variant="elevated"
              size="medium"
              onPress={() => handleButtonPress('elevated-1')}
              loading={loadingStates['elevated-1']}
            >
              Elevated
            </M3Button>
            <M3Button
              variant="elevated"
              size="medium"
              icon="star"
              onPress={() => handleButtonPress('elevated-2')}
            >
              With Icon
            </M3Button>
            <M3Button variant="elevated" size="medium" disabled>
              Disabled
            </M3Button>
          </ButtonRow>
        </ButtonSection>

        {/* 按钮尺寸 */}
        <ButtonSection title="按钮尺寸 (Button Sizes)">
          <ButtonRow label="Small Buttons (32dp)">
            <M3Button variant="filled" size="small">
              Small
            </M3Button>
            <M3Button variant="tonal" size="small" icon="star">
              Small
            </M3Button>
            <M3Button variant="outlined" size="small">
              Small
            </M3Button>
          </ButtonRow>

          <ButtonRow label="Medium Buttons (40dp) - 默认">
            <M3Button variant="filled" size="medium">
              Medium
            </M3Button>
            <M3Button variant="tonal" size="medium" icon="star">
              Medium
            </M3Button>
            <M3Button variant="outlined" size="medium">
              Medium
            </M3Button>
          </ButtonRow>

          <ButtonRow label="Large Buttons (48dp)">
            <M3Button variant="filled" size="large">
              Large
            </M3Button>
            <M3Button variant="tonal" size="large" icon="star">
              Large
            </M3Button>
            <M3Button variant="outlined" size="large">
              Large
            </M3Button>
          </ButtonRow>
        </ButtonSection>

        {/* 图标位置 */}
        <ButtonSection title="图标位置 (Icon Positioning)">
          <ButtonRow label="Leading Icons">
            <M3Button variant="filled" icon="add" iconPosition="leading">
              Leading Icon
            </M3Button>
            <M3Button variant="outlined" icon="edit" iconPosition="leading">
              Edit
            </M3Button>
          </ButtonRow>

          <ButtonRow label="Trailing Icons">
            <M3Button
              variant="filled"
              icon="arrow_forward"
              iconPosition="trailing"
            >
              Trailing Icon
            </M3Button>
            <M3Button
              variant="outlined"
              icon="open_in_new"
              iconPosition="trailing"
            >
              Open Link
            </M3Button>
          </ButtonRow>
        </ButtonSection>

        {/* 全宽按钮 */}
        <ButtonSection title="全宽按钮 (Full Width)">
          <M3Button variant="filled" fullWidth size="large">
            Full Width Button
          </M3Button>
          <M3Button variant="outlined" fullWidth size="medium" icon="download">
            Download
          </M3Button>
        </ButtonSection>

        {/* 加载状态 */}
        <ButtonSection title="加载状态 (Loading States)">
          <ButtonRow label="Loading Buttons">
            <M3Button variant="filled" loading>
              Loading...
            </M3Button>
            <M3Button variant="tonal" loading>
              Processing
            </M3Button>
            <M3Button variant="outlined" loading>
              Saving
            </M3Button>
          </ButtonRow>
        </ButtonSection>

        {/* 常见用例 */}
        <ButtonSection title="常见用例 (Common Use Cases)">
          <ButtonRow label="表单操作">
            <M3Button variant="filled" icon="check">
              确认
            </M3Button>
            <M3Button variant="outlined">取消</M3Button>
          </ButtonRow>

          <ButtonRow label="导航操作">
            <M3Button variant="tonal" icon="arrow_back" iconPosition="leading">
              返回
            </M3Button>
            <M3Button
              variant="filled"
              icon="arrow_forward"
              iconPosition="trailing"
            >
              下一步
            </M3Button>
          </ButtonRow>

          <ButtonRow label="社交操作">
            <M3Button variant="filled" icon="favorite" size="small">
              点赞
            </M3Button>
            <M3Button variant="outlined" icon="share" size="small">
              分享
            </M3Button>
            <M3Button variant="text" icon="bookmark" size="small">
              收藏
            </M3Button>
          </ButtonRow>
        </ButtonSection>

        {/* 底部间距 */}
        <Box className="h-20" />
      </ScrollView>
    </SafeAreaView>
  );
}
