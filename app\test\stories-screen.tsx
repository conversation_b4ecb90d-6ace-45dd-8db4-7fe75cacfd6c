import React from 'react';
import { ScrollView as RNScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { StoryTabs } from '@/features/stories/components/story-tabs';
import { StoryList } from '@/features/stories/components/story-list';
import {
  useStoriesScreenTest,
  TestScenario,
} from '@/features/stories/hooks/use-stories-screen-test';
import { useRouter } from 'expo-router';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ScrollView } from '@/components/ui/scroll-view';
import { SafeAreaView } from '@/components/ui/safe-area-view';

/**
 * Test screen for the stories screen functionality
 * This screen allows testing different scenarios for the stories screen
 */
export default function StoriesScreenTest() {
  const { t } = useTranslation();
  const router = useRouter();

  // Use the test hook
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
    setTestScenario,
    currentScenario,
  } = useStoriesScreenTest();

  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };

  // Render a scenario button
  const renderScenarioButton = (scenario: TestScenario, label: string) => {
    const isActive = currentScenario === scenario;

    return (
      <Button
        className={`px-4 py-2 rounded-lg border ${
          isActive
            ? 'bg-primary-500 border-primary-500 dark:bg-primary-600 dark:border-primary-600'
            : 'bg-transparent border-outline-300 dark:border-outline-600'
        }`}
        onPress={() => setTestScenario(scenario)}
      >
        <Text
          className={`text-xs ${
            isActive
              ? 'text-typography-950 dark:text-typography-50'
              : 'text-typography-700 dark:text-typography-300'
          }`}
        >
          {label}
        </Text>
      </Button>
    );
  };

  return (
    <SafeAreaView
      className="flex-1 bg-background-50 dark:bg-background-900"
      edges={['top', 'left', 'right']}
    >
      <Box className="p-4 bg-background-100 dark:bg-background-800 border-b border-outline-200 dark:border-outline-700">
        <Text className="text-lg font-bold text-typography-900 dark:text-typography-100 mb-2">
          Stories Screen Test
        </Text>

        <RNScrollView horizontal showsHorizontalScrollIndicator={false}>
          <Box className="flex-row flex-wrap gap-2 mb-4">
            {renderScenarioButton(TestScenario.SUCCESS, 'Success')}
            {renderScenarioButton(TestScenario.EMPTY, 'Empty')}
            {renderScenarioButton(TestScenario.LOADING, 'Loading')}
            {renderScenarioButton(TestScenario.ERROR_NETWORK, 'Network Error')}
            {renderScenarioButton(TestScenario.ERROR_SERVER, 'Server Error')}
            {renderScenarioButton(TestScenario.ERROR_AUTH, 'Auth Error')}
            {renderScenarioButton(TestScenario.LARGE_DATASET, 'Large Dataset')}
          </Box>
        </RNScrollView>

        <Button
          className="mt-4 p-4 bg-primary-500 dark:bg-primary-600 rounded-lg items-center"
          onPress={() => router.back()}
        >
          <Text className="text-typography-950 dark:text-typography-50 font-bold">
            Back to App
          </Text>
        </Button>
      </Box>

      <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />

      <Box className="flex-1">
        <StoryList
          stories={stories}
          onStoryPress={handleStoryPress}
          isLoading={isLoading}
          isRefreshing={isRefreshing}
          onRefresh={refreshStories}
          onLoadMore={loadMoreStories}
          hasMoreStories={hasMoreStories}
          error={error}
          onRetry={retryFetch}
          retryCount={retryCount}
        />
      </Box>
    </SafeAreaView>
  );
}
