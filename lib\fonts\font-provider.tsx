'use client';
import React, { useEffect, ReactNode } from 'react';
import { Text } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';
import {
  useFonts,
  Roboto_300Light,
  Roboto_300Light_Italic,
  Roboto_400Regular,
  Roboto_400Regular_Italic,
  Roboto_500Medium,
  Roboto_500Medium_Italic,
  Roboto_700Bold,
  Roboto_700Bold_Italic,
  Roboto_900Black,
  Roboto_900Black_Italic,
} from '@expo-google-fonts/roboto';

import {
  RobotoCondensed_300Light,
  RobotoCondensed_300Light_Italic,
  RobotoCondensed_400Regular,
  RobotoCondensed_400Regular_Italic,
  RobotoCondensed_700Bold,
  RobotoCondensed_700Bold_Italic,
} from '@expo-google-fonts/roboto-condensed';

import {
  RobotoMono_100Thin,
  RobotoMono_100Thin_Italic,
  RobotoMono_200ExtraLight,
  RobotoMono_200ExtraLight_Italic,
  RobotoMono_300Light,
  RobotoMono_300Light_Italic,
  RobotoMono_400Regular,
  RobotoMono_400Regular_Italic,
  RobotoMono_500Medium,
  RobotoMono_500Medium_Italic,
  RobotoMono_600SemiBold,
  RobotoMono_600SemiBold_Italic,
  RobotoMono_700Bold,
  RobotoMono_700Bold_Italic,
} from '@expo-google-fonts/roboto-mono';

// 防止闪屏自动隐藏
SplashScreen.preventAutoHideAsync();

interface FontProviderProps {
  children: ReactNode;
}

export function FontProvider({ children }: FontProviderProps) {
  const [fontsLoaded, fontError] = useFonts({
    // Roboto 主要字体
    Roboto_300Light,
    Roboto_300Light_Italic,
    Roboto_400Regular,
    Roboto_400Regular_Italic,
    Roboto_500Medium,
    Roboto_500Medium_Italic,
    Roboto_700Bold,
    Roboto_700Bold_Italic,
    Roboto_900Black,
    Roboto_900Black_Italic,

    // Roboto Condensed
    RobotoCondensed_300Light,
    RobotoCondensed_300Light_Italic,
    RobotoCondensed_400Regular,
    RobotoCondensed_400Regular_Italic,
    RobotoCondensed_700Bold,
    RobotoCondensed_700Bold_Italic,

    // Roboto Mono
    RobotoMono_100Thin,
    RobotoMono_100Thin_Italic,
    RobotoMono_200ExtraLight,
    RobotoMono_200ExtraLight_Italic,
    RobotoMono_300Light,
    RobotoMono_300Light_Italic,
    RobotoMono_400Regular,
    RobotoMono_400Regular_Italic,
    RobotoMono_500Medium,
    RobotoMono_500Medium_Italic,
    RobotoMono_600SemiBold,
    RobotoMono_600SemiBold_Italic,
    RobotoMono_700Bold,
    RobotoMono_700Bold_Italic,
  });

  useEffect(() => {
    if (fontsLoaded || fontError) {
      // 字体加载完成或出错时隐藏闪屏
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  // 字体加载中的回调处理
  const onLayoutRootView = React.useCallback(async () => {
    if (fontsLoaded || fontError) {
      await SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  if (!fontsLoaded && !fontError) {
    // 字体还在加载中，返回null或加载指示器
    return null;
  }

  if (fontError) {
    // 字体加载出错，显示错误信息
    console.error('Font loading error:', fontError);
    return (
      <Text style={{ fontSize: 16, color: 'red', padding: 20 }}>
        Error loading fonts: {fontError.message}
      </Text>
    );
  }

  return <>{children}</>;
}

// 用于检查字体是否加载完成的Hook
export function useFontLoaded() {
  const [fontsLoaded] = useFonts({
    Roboto_400Regular,
  });

  return fontsLoaded;
}
