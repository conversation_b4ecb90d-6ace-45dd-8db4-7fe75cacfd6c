'use client';
import React from 'react';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ScrollView } from '@/components/ui/scroll-view';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import {
  M3Button,
  M3ButtonText,
  M3ButtonIcon,
} from '@/components/ui/button/material3-button';
import {
  M3Fab,
  M3FabIcon,
  M3ExtendedFab,
  M3ExtendedFabIcon,
  M3ExtendedFabText,
} from '@/components/ui/fab/material3-fab';
import { MaterialSymbol } from '@/lib/icons/material-symbols';
import {
  M3HeadlineMedium,
  M3TitleMedium,
  M3BodyMedium,
  M3LabelLarge,
  M3LabelSmall,
} from '@/components/ui/text/material3-text';
import { useTheme } from '@/lib/theme/theme-provider';

export default function M3CompleteTestPage() {
  const { themeColors, toggleTheme, currentTheme } = useTheme();

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: themeColors.background.main }}
    >
      <ScrollView className="flex-1">
        <VStack space="lg" className="p-6">
          {/* Header */}
          <View className="mb-6">
            <M3HeadlineMedium color={themeColors.primary.main}>
              Material Design 3 Expressive
            </M3HeadlineMedium>
            <M3BodyMedium color={themeColors.surface.on}>
              完整组件展示页面
            </M3BodyMedium>
          </View>

          {/* Theme Toggle */}
          <View className="mb-6">
            <M3TitleMedium className="mb-3" color={themeColors.primary.main}>
              主题切换
            </M3TitleMedium>
            <M3Button
              variant="filled-tonal"
              size="medium"
              onPress={toggleTheme}
            >
              <M3ButtonIcon iconPosition="leading">
                <MaterialSymbol
                  name={currentTheme === 'light' ? 'sunny' : 'cloudy'}
                  size="medium"
                  color={themeColors.primary.onContainer}
                />
              </M3ButtonIcon>
              <M3ButtonText>
                {currentTheme === 'light' ? '切换到深色模式' : '切换到浅色模式'}
              </M3ButtonText>
            </M3Button>
          </View>

          {/* Material 3 Buttons */}
          <View className="mb-6">
            <M3TitleMedium className="mb-3" color={themeColors.primary.main}>
              Material 3 按钮
            </M3TitleMedium>
            <VStack space="sm">
              <HStack space="sm" className="flex-wrap">
                <M3Button variant="filled" size="medium">
                  <M3ButtonText>Filled</M3ButtonText>
                </M3Button>
                <M3Button variant="filled-tonal" size="medium">
                  <M3ButtonText>Filled Tonal</M3ButtonText>
                </M3Button>
                <M3Button variant="outlined" size="medium">
                  <M3ButtonText>Outlined</M3ButtonText>
                </M3Button>
              </HStack>
              <HStack space="sm" className="flex-wrap">
                <M3Button variant="text" size="medium">
                  <M3ButtonText>Text</M3ButtonText>
                </M3Button>
                <M3Button variant="elevated" size="medium">
                  <M3ButtonText>Elevated</M3ButtonText>
                </M3Button>
              </HStack>
            </VStack>
          </View>

          {/* Material 3 Buttons with Icons */}
          <View className="mb-6">
            <M3TitleMedium className="mb-3" color={themeColors.primary.main}>
              带图标的按钮
            </M3TitleMedium>
            <VStack space="sm">
              <M3Button variant="filled" size="medium">
                <M3ButtonIcon iconPosition="leading">
                  <MaterialSymbol
                    name="add"
                    size="medium"
                    color={themeColors.primary.on}
                  />
                </M3ButtonIcon>
                <M3ButtonText>添加</M3ButtonText>
              </M3Button>
              <M3Button variant="outlined" size="medium">
                <M3ButtonText>编辑</M3ButtonText>
                <M3ButtonIcon iconPosition="trailing">
                  <MaterialSymbol
                    name="edit"
                    size="medium"
                    color={themeColors.primary.main}
                  />
                </M3ButtonIcon>
              </M3Button>
            </VStack>
          </View>

          {/* Material 3 FABs */}
          <View className="mb-6">
            <M3TitleMedium className="mb-3" color={themeColors.primary.main}>
              浮动操作按钮 (FAB)
            </M3TitleMedium>
            <HStack space="md" className="items-center">
              <M3Fab variant="primary" size="small">
                <M3FabIcon>
                  <MaterialSymbol
                    name="add"
                    size="small"
                    color={themeColors.primary.on}
                  />
                </M3FabIcon>
              </M3Fab>
              <M3Fab variant="primary" size="medium">
                <M3FabIcon>
                  <MaterialSymbol
                    name="edit"
                    size="medium"
                    color={themeColors.primary.on}
                  />
                </M3FabIcon>
              </M3Fab>
              <M3Fab variant="secondary" size="medium">
                <M3FabIcon>
                  <MaterialSymbol
                    name="favorite"
                    size="medium"
                    color={themeColors.secondary.on}
                  />
                </M3FabIcon>
              </M3Fab>
            </HStack>
          </View>

          {/* Extended FAB */}
          <View className="mb-6">
            <M3TitleMedium className="mb-3" color={themeColors.primary.main}>
              扩展 FAB
            </M3TitleMedium>
            <M3ExtendedFab variant="primary" size="medium">
              <M3ExtendedFabIcon iconPosition="leading">
                <MaterialSymbol
                  name="auto_stories"
                  size="medium"
                  color={themeColors.primary.on}
                />
              </M3ExtendedFabIcon>
              <M3ExtendedFabText>创建故事</M3ExtendedFabText>
            </M3ExtendedFab>
          </View>

          {/* Material Symbols Showcase */}
          <View className="mb-6">
            <M3TitleMedium className="mb-3" color={themeColors.primary.main}>
              Material Symbols 图标
            </M3TitleMedium>
            <HStack space="md" className="flex-wrap">
              {[
                'home',
                'search',
                'favorite',
                'star',
                'share',
                'person',
                'settings',
                'notifications',
                'edit',
                'delete',
              ].map((iconName) => (
                <View key={iconName} className="items-center mb-2">
                  <MaterialSymbol
                    name={iconName as any}
                    size="large"
                    color={themeColors.primary.main}
                  />
                  <M3LabelSmall className="mt-1">{iconName}</M3LabelSmall>
                </View>
              ))}
            </HStack>
          </View>

          {/* Color Palette Showcase */}
          <View className="mb-6">
            <M3TitleMedium className="mb-3" color={themeColors.primary.main}>
              Material 3 色彩系统
            </M3TitleMedium>
            <VStack space="sm">
              <HStack space="sm" className="items-center">
                <View
                  className="w-8 h-8 rounded-md"
                  style={{ backgroundColor: themeColors.primary.main }}
                />
                <M3BodyMedium>Primary</M3BodyMedium>
              </HStack>
              <HStack space="sm" className="items-center">
                <View
                  className="w-8 h-8 rounded-md"
                  style={{ backgroundColor: themeColors.secondary.main }}
                />
                <M3BodyMedium>Secondary</M3BodyMedium>
              </HStack>
              <HStack space="sm" className="items-center">
                <View
                  className="w-8 h-8 rounded-md"
                  style={{ backgroundColor: themeColors.tertiary.main }}
                />
                <M3BodyMedium>Tertiary</M3BodyMedium>
              </HStack>
              <HStack space="sm" className="items-center">
                <View
                  className="w-8 h-8 rounded-md"
                  style={{ backgroundColor: themeColors.error.main }}
                />
                <M3BodyMedium>Error</M3BodyMedium>
              </HStack>
            </VStack>
          </View>

          {/* Typography Showcase (shortened) */}
          <View className="mb-6">
            <M3TitleMedium className="mb-3" color={themeColors.primary.main}>
              Typography 示例
            </M3TitleMedium>
            <VStack space="xs">
              <M3HeadlineMedium>Headline Medium</M3HeadlineMedium>
              <M3TitleMedium>Title Medium</M3TitleMedium>
              <M3BodyMedium>Body Medium - 这是正文文本</M3BodyMedium>
              <M3LabelLarge>Label Large</M3LabelLarge>
            </VStack>
          </View>

          <View className="h-20" />
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
}
 