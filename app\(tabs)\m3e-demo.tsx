import React, { useState } from 'react';
import { ScrollView, View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@/hooks/use-theme';
import { 
  M3EButtons, 
  M3EButtonGroups, 
  M3EFAB, 
  M3EFABMenu,
  type M3EFABMenuItem 
} from '@/components/ui/m3e-button';
import { Ionicons } from '@expo/vector-icons';

export default function M3EDemo() {
  const { currentTheme } = useTheme();
  const [selectedGroupIndex, setSelectedGroupIndex] = useState(0);
  const [isFABMenuOpen, setIsFABMenuOpen] = useState(false);

  const isDark = currentTheme === 'dark';

  const fabMenuItems: M3EFABMenuItem[] = [
    {
      icon: <Ionicons name="add" size={24} color={isDark ? '#4F378A' : '#4F378A'} />,
      label: 'Create',
      onPress: () => console.log('Create pressed'),
    },
    {
      icon: <Ionicons name="edit" size={24} color={isDark ? '#4F378A' : '#4F378A'} />,
      label: 'Edit',
      onPress: () => console.log('Edit pressed'),
    },
    {
      icon: <Ionicons name="share" size={24} color={isDark ? '#4F378A' : '#4F378A'} />,
      label: 'Share',
      onPress: () => console.log('Share pressed'),
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? '#1D1B20' : '#FEF7FF',
    },
    scrollContent: {
      padding: 16,
    },
    section: {
      marginBottom: 32,
    },
    sectionTitle: {
      fontSize: 24,
      fontWeight: '600',
      color: isDark ? '#E6E0E9' : '#1D1B20',
      marginBottom: 16,
    },
    subsectionTitle: {
      fontSize: 18,
      fontWeight: '500',
      color: isDark ? '#E6E0E9' : '#1D1B20',
      marginBottom: 12,
      marginTop: 16,
    },
    buttonRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 12,
      marginBottom: 16,
    },
    fabContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16,
      marginBottom: 16,
    },
    fabMenuContainer: {
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'center',
      height: 200,
      marginBottom: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollContent}>
        {/* M3E Buttons */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>M3E Buttons</Text>
          
          <Text style={styles.subsectionTitle}>Filled Buttons</Text>
          <View style={styles.buttonRow}>
            <M3EButtons
              variant="filled"
              size="small"
              onPress={() => console.log('Small filled pressed')}
            >
              Small
            </M3EButtons>
            <M3EButtons
              variant="filled"
              size="medium"
              onPress={() => console.log('Medium filled pressed')}
            >
              Medium
            </M3EButtons>
            <M3EButtons
              variant="filled"
              size="large"
              onPress={() => console.log('Large filled pressed')}
            >
              Large
            </M3EButtons>
          </View>

          <Text style={styles.subsectionTitle}>Outlined Buttons</Text>
          <View style={styles.buttonRow}>
            <M3EButtons
              variant="outlined"
              size="small"
              onPress={() => console.log('Small outlined pressed')}
            >
              Small
            </M3EButtons>
            <M3EButtons
              variant="outlined"
              size="medium"
              onPress={() => console.log('Medium outlined pressed')}
            >
              Medium
            </M3EButtons>
            <M3EButtons
              variant="outlined"
              size="large"
              onPress={() => console.log('Large outlined pressed')}
            >
              Large
            </M3EButtons>
          </View>

          <Text style={styles.subsectionTitle}>Text Buttons</Text>
          <View style={styles.buttonRow}>
            <M3EButtons
              variant="text"
              size="small"
              onPress={() => console.log('Small text pressed')}
            >
              Small
            </M3EButtons>
            <M3EButtons
              variant="text"
              size="medium"
              onPress={() => console.log('Medium text pressed')}
            >
              Medium
            </M3EButtons>
            <M3EButtons
              variant="text"
              size="large"
              onPress={() => console.log('Large text pressed')}
            >
              Large
            </M3EButtons>
          </View>

          <Text style={styles.subsectionTitle}>With Icons</Text>
          <View style={styles.buttonRow}>
            <M3EButtons
              variant="filled"
              size="medium"
              icon={<Ionicons name="add" size={18} color="#FFFFFF" />}
              onPress={() => console.log('Icon button pressed')}
            >
              Add Item
            </M3EButtons>
            <M3EButtons
              variant="outlined"
              size="medium"
              icon={<Ionicons name="download" size={18} color={isDark ? '#D0BCFF' : '#6750A4'} />}
              onPress={() => console.log('Download pressed')}
            >
              Download
            </M3EButtons>
          </View>
        </View>

        {/* M3E Button Groups */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>M3E Button Groups</Text>
          
          <Text style={styles.subsectionTitle}>Round Button Groups</Text>
          <View style={styles.buttonRow}>
            <M3EButtonGroups
              type="round"
              size="medium"
              slots={3}
              labels={['First', 'Second', 'Third']}
              selectedIndex={selectedGroupIndex}
              onPress={setSelectedGroupIndex}
            />
          </View>

          <Text style={styles.subsectionTitle}>Square Button Groups</Text>
          <View style={styles.buttonRow}>
            <M3EButtonGroups
              type="square"
              size="medium"
              slots={4}
              labels={['Option A', 'Option B', 'Option C', 'Option D']}
              selectedIndex={0}
              onPress={(index) => console.log('Square group pressed:', index)}
            />
          </View>

          <Text style={styles.subsectionTitle}>Different Sizes</Text>
          <View style={styles.buttonRow}>
            <M3EButtonGroups
              type="round"
              size="small"
              slots={2}
              labels={['Yes', 'No']}
              selectedIndex={0}
              onPress={(index) => console.log('Small group pressed:', index)}
            />
          </View>
          <View style={styles.buttonRow}>
            <M3EButtonGroups
              type="round"
              size="large"
              slots={3}
              labels={['Small', 'Medium', 'Large']}
              selectedIndex={1}
              onPress={(index) => console.log('Large group pressed:', index)}
            />
          </View>
        </View>

        {/* M3E FAB */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>M3E FAB</Text>
          
          <Text style={styles.subsectionTitle}>Standard FABs</Text>
          <View style={styles.fabContainer}>
            <M3EFAB
              size="small"
              variant="primary"
              icon={<Ionicons name="add" size={18} color="#FFFFFF" />}
              onPress={() => console.log('Small FAB pressed')}
            />
            <M3EFAB
              size="medium"
              variant="primary"
              icon={<Ionicons name="add" size={24} color="#FFFFFF" />}
              onPress={() => console.log('Medium FAB pressed')}
            />
            <M3EFAB
              size="large"
              variant="primary"
              icon={<Ionicons name="add" size={36} color="#FFFFFF" />}
              onPress={() => console.log('Large FAB pressed')}
            />
          </View>

          <Text style={styles.subsectionTitle}>Different Variants</Text>
          <View style={styles.fabContainer}>
            <M3EFAB
              size="medium"
              variant="primary-container"
              icon={<Ionicons name="edit" size={24} color={isDark ? '#EADDFF' : '#4F378A'} />}
              onPress={() => console.log('Primary container FAB pressed')}
            />
            <M3EFAB
              size="medium"
              variant="secondary-container"
              icon={<Ionicons name="share" size={24} color={isDark ? '#E8DEF8' : '#4A4458'} />}
              onPress={() => console.log('Secondary container FAB pressed')}
            />
            <M3EFAB
              size="medium"
              variant="tertiary-container"
              icon={<Ionicons name="heart" size={24} color={isDark ? '#FFD8E4' : '#633B48'} />}
              onPress={() => console.log('Tertiary container FAB pressed')}
            />
          </View>

          <Text style={styles.subsectionTitle}>Extended FABs</Text>
          <View style={styles.fabContainer}>
            <M3EFAB
              size="medium"
              variant="primary"
              extended={true}
              icon={<Ionicons name="add" size={20} color="#FFFFFF" />}
              label="Create New"
              onPress={() => console.log('Extended FAB pressed')}
            />
            <M3EFAB
              size="large"
              variant="primary-container"
              extended={true}
              icon={<Ionicons name="edit" size={24} color={isDark ? '#EADDFF' : '#4F378A'} />}
              label="Edit Document"
              onPress={() => console.log('Large extended FAB pressed')}
            />
          </View>
        </View>

        {/* M3E FAB Menu */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>M3E FAB Menu</Text>
          
          <Text style={styles.subsectionTitle}>Primary Container Menu</Text>
          <View style={styles.fabMenuContainer}>
            <M3EFABMenu
              variant="primary-container"
              segments={3}
              items={fabMenuItems}
              isOpen={isFABMenuOpen}
              onToggle={setIsFABMenuOpen}
              fabIcon={<Ionicons name="add" size={24} color={isDark ? '#EADDFF' : '#4F378A'} />}
              fabOpenIcon={<Ionicons name="close" size={24} color={isDark ? '#EADDFF' : '#4F378A'} />}
              direction="up"
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
