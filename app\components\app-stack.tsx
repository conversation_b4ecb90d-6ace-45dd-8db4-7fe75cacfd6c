import React from 'react';
import { Stack } from 'expo-router';
import { ConnectionStatus } from '@/components/shared/connection-status';
import { Box } from '@/components/ui/box';

export default function AppStack() {
  return (
    <Box className="flex-1">
      <ConnectionStatus />
      <Stack screenOptions={{ headerShown: false }}>
        {/* Define all top-level layouts/routes here */}
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen name="(settings)" options={{ headerShown: false }} />
        <Stack.Screen name="(profile)" options={{ headerShown: false }} />
        <Stack.Screen name="stories/[id]" options={{ headerShown: false }} />
        <Stack.Screen name="rankings/index" options={{ headerShown: true }} />
        <Stack.Screen name="+not-found" options={{ title: 'Oops!' }} />
      </Stack>
    </Box>
  );
}
