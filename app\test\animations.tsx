import React, { useState, useRef } from 'react';
import {
  ScrollView as RNScrollView,
  Animated,
  Easing,
  View,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { StoryTabs } from '@/features/stories/components/story-tabs';
import { StoryList } from '@/features/stories/components/story-list';
import { useStoriesScreenTest } from '@/features/stories/hooks/use-stories-screen-test';
import { RefreshCw, ChevronDown, ChevronUp } from 'lucide-react-native';
import AnimationCard from './components/animation-card';
import AnimationPreview from './components/animation-preview';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { SafeAreaView } from '@/components/ui/safe-area-view';

/**
 * Animations test screen
 * This screen tests the animations in the app
 */
export default function AnimationsTest() {
  const { t } = useTranslation();
  const router = useRouter();

  // Use the test hook
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
  } = useStoriesScreenTest();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const spinValue = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Animation types
  const animationTypes = [
    {
      id: 'fade',
      title: 'Fade In/Out',
      description: 'Fade animation for smooth transitions',
      run: () => {
        Animated.sequence([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]).start();
      },
    },
    {
      id: 'slide',
      title: 'Slide In/Out',
      description: 'Slide animation for entrance and exit',
      run: () => {
        Animated.sequence([
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
            easing: Easing.out(Easing.cubic),
          }),
          Animated.timing(slideAnim, {
            toValue: -100,
            duration: 1000,
            useNativeDriver: true,
            easing: Easing.in(Easing.cubic),
          }),
        ]).start();
      },
    },
    {
      id: 'spin',
      title: 'Spin',
      description: 'Rotation animation for loading indicators',
      run: () => {
        spinValue.setValue(0);
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
          easing: Easing.linear,
        }).start();
      },
    },
    {
      id: 'scale',
      title: 'Scale',
      description: 'Scale animation for emphasis',
      run: () => {
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
            easing: Easing.out(Easing.cubic),
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
            easing: Easing.in(Easing.cubic),
          }),
        ]).start();
      },
    },
  ];

  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };

  // Interpolate spin value to rotation
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <SafeAreaView className="flex-1 bg-background-50 dark:bg-background-900" edges={['top', 'left', 'right']}>
      <RNScrollView>
        <Box className="p-4 bg-background-100 dark:bg-background-800 border-b border-outline-200 dark:border-outline-700">
          <Text className="text-lg font-bold text-typography-900 dark:text-typography-100 mb-2">
            Animations Test
          </Text>

          <AnimationPreview
            fadeAnim={fadeAnim}
            slideAnim={slideAnim}
            spin={spin}
            scaleAnim={scaleAnim}
          />

          <Box className="mb-4">
            <Text className="text-sm font-bold text-typography-900 dark:text-typography-100 mb-2">
              Animation Types
            </Text>

            {animationTypes.map((animation) => (
              <AnimationCard
                key={animation.id}
                animation={animation}
              />
            ))}
          </Box>

          <Button
            className="mt-4 p-4 bg-primary-500 dark:bg-primary-600 rounded-lg items-center"
            onPress={() => router.back()}
          >
            <Text className="text-typography-950 dark:text-typography-50 font-bold">
              Back to Tests
            </Text>
          </Button>
        </Box>

        <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />

        <View style={{ height: 400 }}>
          <StoryList
            stories={stories}
            onStoryPress={handleStoryPress}
            isLoading={isLoading}
            isRefreshing={isRefreshing}
            onRefresh={refreshStories}
            onLoadMore={loadMoreStories}
            hasMoreStories={hasMoreStories}
            error={error}
            onRetry={retryFetch}
            retryCount={retryCount}
          />
        </View>
      </RNScrollView>
    </SafeAreaView>
  );
}
