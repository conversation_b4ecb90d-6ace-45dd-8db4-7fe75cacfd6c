import React from 'react';
import { ScrollView as RNScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { StoryTabs } from '@/features/stories/components/story-tabs';
import { StoryList } from '@/features/stories/components/story-list';
import { useStoriesScreenTest, TestScenario } from '@/features/stories/hooks/use-stories-screen-test';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { SafeAreaView } from '@/components/ui/safe-area-view';
import { Pressable } from '@/components/ui/pressable';

/**
 * Error handling test screen
 * This screen tests the error handling capabilities of the app
 */
export default function ErrorHandlingTest() {
  const { t } = useTranslation();
  const router = useRouter();
  
  // Use the test hook
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
    setTestScenario,
    currentScenario,
  } = useStoriesScreenTest();
  
  // Error scenarios
  const errorScenarios = [
    {
      id: TestScenario.SUCCESS,
      title: 'Success',
      description: 'No errors, normal operation',
    },
    {
      id: TestScenario.ERROR_NETWORK,
      title: 'Network Error',
      description: 'Simulate a network connection error',
    },
    {
      id: TestScenario.ERROR_SERVER,
      title: 'Server Error',
      description: 'Simulate a server error (500)',
    },
    {
      id: TestScenario.ERROR_AUTH,
      title: 'Authentication Error',
      description: 'Simulate an authentication error',
    },
    {
      id: TestScenario.EMPTY,
      title: 'Empty Data',
      description: 'Simulate empty data response',
    },
  ];
  
  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };
  
  return (
    <SafeAreaView className="flex-1 bg-background-50 dark:bg-background-900" edges={['top', 'left', 'right']}>
      <Box className="p-4 bg-background-100 dark:bg-background-800 border-b border-outline-200 dark:border-outline-700">
        <Text className="text-lg font-bold text-typography-900 dark:text-typography-100 mb-2">
          Error Handling Test
        </Text>
        
        <Box className="bg-primary-100 dark:bg-primary-900 p-2 rounded-lg mb-4">
          <Text className="text-sm font-bold text-primary-700 dark:text-primary-300">
            Current Scenario: {errorScenarios.find(s => s.id === currentScenario)?.title || 'Unknown'}
          </Text>
        </Box>
        
        <Box className="mb-4">
          <Text className="text-sm font-bold text-typography-900 dark:text-typography-100 mb-2">
            Error Scenarios
          </Text>
          
          <RNScrollView>
            {errorScenarios.map((scenario) => (
              <Pressable
                key={scenario.id}
                className={`bg-background-50 dark:bg-background-800 rounded-lg p-4 mb-2 border ${
                  currentScenario === scenario.id 
                    ? 'border-primary-500 dark:border-primary-600' 
                    : 'border-outline-200 dark:border-outline-700'
                }`}
                onPress={() => setTestScenario(scenario.id)}
              >
                <Text className="text-base font-bold text-typography-900 dark:text-typography-100 mb-1">
                  {scenario.title}
                </Text>
                <Text className="text-sm text-typography-600 dark:text-typography-400">
                  {scenario.description}
                </Text>
              </Pressable>
            ))}
          </RNScrollView>
        </Box>
        
        <Button
          className="mt-4 p-4 bg-primary-500 dark:bg-primary-600 rounded-lg items-center"
          onPress={() => router.back()}
        >
          <Text className="text-typography-950 dark:text-typography-50 font-bold">
            Back to Tests
          </Text>
        </Button>
      </Box>
      
      <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />
      
      <Box className="flex-1">
        <StoryList
          stories={stories}
          onStoryPress={handleStoryPress}
          isLoading={isLoading}
          isRefreshing={isRefreshing}
          onRefresh={refreshStories}
          onLoadMore={loadMoreStories}
          hasMoreStories={hasMoreStories}
          error={error}
          onRetry={retryFetch}
          retryCount={retryCount}
        />
      </Box>
    </SafeAreaView>
  );
}
