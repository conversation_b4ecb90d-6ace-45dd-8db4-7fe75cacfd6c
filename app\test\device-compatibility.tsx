import React, { useState } from 'react';
import { ScrollView as RNScrollView, Dimensions, View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { StoryTabs } from '@/features/stories/components/story-tabs';
import { StoryList } from '@/features/stories/components/story-list';
import { useStoriesScreenTest } from '@/features/stories/hooks/use-stories-screen-test';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { SafeAreaView } from '@/components/ui/safe-area-view';
import { Pressable } from '@/components/ui/pressable';

/**
 * Test screen for device compatibility testing
 * This screen simulates different device sizes and orientations
 */
export default function DeviceCompatibilityTest() {
  const { t } = useTranslation();
  const router = useRouter();
  
  // Get actual device dimensions
  const actualWidth = Dimensions.get('window').width;
  const actualHeight = Dimensions.get('window').height;
  
  // Device simulation options
  const deviceSizes = [
    { name: 'Actual Device', width: actualWidth, height: actualHeight },
    { name: 'iPhone SE', width: 375, height: 667 },
    { name: 'iPhone X/11/12/13', width: 390, height: 844 },
    { name: 'iPhone Pro Max', width: 428, height: 926 },
    { name: 'Small Android', width: 360, height: 640 },
    { name: 'Medium Android', width: 393, height: 851 },
    { name: 'Large Android', width: 412, height: 915 },
    { name: 'iPad', width: 768, height: 1024 },
    { name: 'Android Tablet', width: 800, height: 1280 },
  ];
  
  // Orientation options
  const orientations = [
    { name: 'Portrait', isPortrait: true },
    { name: 'Landscape', isPortrait: false },
  ];
  
  // State for selected device and orientation
  const [selectedDevice, setSelectedDevice] = useState(deviceSizes[0]);
  const [selectedOrientation, setSelectedOrientation] = useState(orientations[0]);
  
  // Calculate container dimensions based on selected device and orientation
  const containerWidth = selectedOrientation.isPortrait ? selectedDevice.width : selectedDevice.height;
  const containerHeight = selectedOrientation.isPortrait ? selectedDevice.height : selectedDevice.width;
  
  // Scale factor to fit the device simulation in the screen
  const scaleFactor = Math.min(
    (actualWidth - 40) / containerWidth,
    (actualHeight - 200) / containerHeight,
    1 // Don't scale up, only down
  );
  
  // Use the stories screen test hook
  const {
    stories,
    isLoading,
    isRefreshing,
    error,
    activeTab,
    setActiveTab,
    refreshStories,
    loadMoreStories,
    hasMoreStories,
    retryFetch,
    retryCount,
  } = useStoriesScreenTest();
  
  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };
  
  return (
    <SafeAreaView className="flex-1 bg-background-50 dark:bg-background-900" edges={['top', 'left', 'right']}>
      <RNScrollView>
        <Box className="p-4 bg-background-100 dark:bg-background-800 border-b border-outline-200 dark:border-outline-700">
          <Text className="text-lg font-bold text-typography-900 dark:text-typography-100 mb-2">
            Device Compatibility Test
          </Text>
          
          <Box className="flex-row justify-between mb-4">
            {/* Device selection */}
            <Box className="flex-1">
              <Text className="text-sm font-bold text-typography-900 dark:text-typography-100 mb-2">
                Device
              </Text>
              <RNScrollView horizontal showsHorizontalScrollIndicator={false}>
                <Box className="flex-row flex-wrap gap-1">
                  {deviceSizes.map((device) => (
                    <Pressable
                      key={device.name}
                      className={`px-2 py-1 rounded border ${
                        selectedDevice.name === device.name 
                          ? 'bg-primary-100 dark:bg-primary-800 border-primary-500 dark:border-primary-600' 
                          : 'border-outline-200 dark:border-outline-700'
                      }`}
                      onPress={() => setSelectedDevice(device)}
                    >
                      <Text
                        className={`text-xs ${
                          selectedDevice.name === device.name 
                            ? 'text-primary-700 dark:text-primary-300' 
                            : 'text-typography-700 dark:text-typography-300'
                        }`}
                      >
                        {device.name}
                      </Text>
                    </Pressable>
                  ))}
                </Box>
              </RNScrollView>
            </Box>
            
            {/* Orientation selection */}
            <Box className="flex-1">
              <Text className="text-sm font-bold text-typography-900 dark:text-typography-100 mb-2">
                Orientation
              </Text>
              <Box className="flex-row flex-wrap gap-1">
                {orientations.map((orientation) => (
                  <Pressable
                    key={orientation.name}
                    className={`px-2 py-1 rounded border ${
                      selectedOrientation.name === orientation.name 
                        ? 'bg-primary-100 dark:bg-primary-800 border-primary-500 dark:border-primary-600' 
                        : 'border-outline-200 dark:border-outline-700'
                    }`}
                    onPress={() => setSelectedOrientation(orientation)}
                  >
                    <Text
                      className={`text-xs ${
                        selectedOrientation.name === orientation.name 
                          ? 'text-primary-700 dark:text-primary-300' 
                          : 'text-typography-700 dark:text-typography-300'
                      }`}
                    >
                      {orientation.name}
                    </Text>
                  </Pressable>
                ))}
              </Box>
            </Box>
          </Box>
          
          <Button
            className="mt-4 p-4 bg-primary-500 dark:bg-primary-600 rounded-lg items-center"
            onPress={() => router.back()}
          >
            <Text className="text-typography-950 dark:text-typography-50 font-bold">
              Back to App
            </Text>
          </Button>
        </Box>
        
        {/* Device simulation */}
        <Box className="items-center justify-center mt-4 mb-4">
          <View
            style={{
              width: containerWidth * scaleFactor,
              height: containerHeight * scaleFactor,
              transform: [{ scale: scaleFactor }],
              transformOrigin: 'top left',
              borderWidth: 2,
              borderColor: '#3b82f6',
              borderRadius: 16,
              overflow: 'hidden',
              backgroundColor: '#ffffff',
            }}
          >
            <Box className="flex-row justify-between p-2 bg-background-100 dark:bg-background-800 border-b border-outline-200 dark:border-outline-700">
              <Text className="text-xs text-typography-600 dark:text-typography-400">
                {selectedDevice.name} - {selectedOrientation.name}
              </Text>
              <Text className="text-xs text-typography-600 dark:text-typography-400">
                {containerWidth}x{containerHeight}
              </Text>
            </Box>
            
            <View style={{ flex: 1, width: containerWidth, height: containerHeight - 30 }}>
              <StoryTabs activeTab={activeTab} onTabPress={setActiveTab} />
              
              <Box className="flex-1">
                <StoryList
                  stories={stories}
                  onStoryPress={handleStoryPress}
                  isLoading={isLoading}
                  isRefreshing={isRefreshing}
                  onRefresh={refreshStories}
                  onLoadMore={loadMoreStories}
                  hasMoreStories={hasMoreStories}
                  error={error}
                  onRetry={retryFetch}
                  retryCount={retryCount}
                />
              </Box>
            </View>
          </View>
        </Box>
      </RNScrollView>
    </SafeAreaView>
  );
}
