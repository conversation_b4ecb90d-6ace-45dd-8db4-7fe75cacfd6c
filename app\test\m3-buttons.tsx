import React, { useState } from 'react';
import { ScrollView, View } from 'react-native';
import { useLocalSearchParams } from 'expo-router';

// M3E Components - 使用新的M3E命名规范
import {
  M3EButtonFilled,
  M3EButtonTonal,
  M3EButtonOutlined,
  M3EButtonText,
  M3EButtonElevated,
} from '@/components/ui/m3e-button/m3e-buttons';
import {
  M3LabelLarge,
  M3LabelSmall,
} from '@/components/ui/text/material3-text';

// Theme
import { useTheme } from '@/lib/theme/theme-provider';

// UI Components
import { SafeAreaView } from '@/components/ui/safe-area-view';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Switch } from '@/components/ui/switch';
import { HeaderBar } from '@/components/ui/header-bar';

const M3EButtonsTestScreen = () => {
  const params = useLocalSearchParams();
  const { toggleTheme, currentTheme } = useTheme();
  const [showSizes, setShowSizes] = useState(false);
  const [showStates, setShowStates] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const isDark = currentTheme === 'dark';

  const handleThemeToggle = () => {
    toggleTheme();
  };

  const handleLoadingTest = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 3000);
  };

  return (
    <SafeAreaView
      className={`flex-1 ${isDark ? 'bg-surface-dark' : 'bg-surface-light'}`}
    >
      <HeaderBar
        title="M3E Expressive Buttons"
        showBackButton={true}
        actions={[
          {
            icon: isDark ? 'light_mode' : 'dark_mode',
            onPress: handleThemeToggle,
            accessibilityLabel: `切换到${isDark ? '浅色' : '深色'}主题`,
          },
        ]}
      />

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <VStack className="p-6 space-y-8">
          {/* 控制选项 */}
          <VStack className="space-y-4">
            <M3LabelLarge
              className={`${
                isDark ? 'text-on-surface-dark' : 'text-on-surface-light'
              }`}
            >
              测试选项
            </M3LabelLarge>

            <HStack className="justify-between items-center">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                显示不同尺寸
              </M3LabelSmall>
              <Switch
                value={showSizes}
                onValueChange={setShowSizes}
                size="sm"
              />
            </HStack>

            <HStack className="justify-between items-center">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                显示交互状态
              </M3LabelSmall>
              <Switch
                value={showStates}
                onValueChange={setShowStates}
                size="sm"
              />
            </HStack>
          </VStack>

          {/* M3E 按钮变体展示 */}
          <VStack className="space-y-6">
            <M3LabelLarge
              className={`${
                isDark ? 'text-on-surface-dark' : 'text-on-surface-light'
              }`}
            >
              M3E 按钮变体 (新命名规范)
            </M3LabelLarge>

            {/* M3EButtonFilled - 主要操作 */}
            <VStack className="space-y-3">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                M3EButtonFilled - 主要操作
              </M3LabelSmall>

              {showSizes ? (
                <VStack className="space-y-3">
                  <HStack className="space-x-3">
                    <M3EButtonFilled size="small">Small</M3EButtonFilled>
                    <M3EButtonFilled size="medium">Medium</M3EButtonFilled>
                    <M3EButtonFilled size="large">Large</M3EButtonFilled>
                  </HStack>
                  <HStack className="space-x-3">
                    <M3EButtonFilled size="small" icon="save">
                      保存
                    </M3EButtonFilled>
                    <M3EButtonFilled
                      size="medium"
                      icon="send"
                      iconPosition="trailing"
                    >
                      发送
                    </M3EButtonFilled>
                    <M3EButtonFilled size="large" icon="cloud_sync">
                      同步
                    </M3EButtonFilled>
                  </HStack>
                </VStack>
              ) : (
                <HStack className="space-x-3 flex-wrap">
                  <M3EButtonFilled
                    onPress={() => console.log('Filled pressed')}
                  >
                    完成
                  </M3EButtonFilled>
                  <M3EButtonFilled icon="save">保存</M3EButtonFilled>
                  <M3EButtonFilled icon="send" iconPosition="trailing">
                    发送
                  </M3EButtonFilled>
                  {showStates && (
                    <>
                      <M3EButtonFilled disabled>禁用</M3EButtonFilled>
                      <M3EButtonFilled
                        loading={isLoading}
                        onPress={handleLoadingTest}
                      >
                        {isLoading ? '处理中...' : '点击加载'}
                      </M3EButtonFilled>
                    </>
                  )}
                </HStack>
              )}
            </VStack>

            {/* M3EButtonTonal - 次要操作 */}
            <VStack className="space-y-3">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                M3EButtonTonal - 次要操作
              </M3LabelSmall>

              {showSizes ? (
                <VStack className="space-y-3">
                  <HStack className="space-x-3">
                    <M3EButtonTonal size="small">Small</M3EButtonTonal>
                    <M3EButtonTonal size="medium">Medium</M3EButtonTonal>
                    <M3EButtonTonal size="large">Large</M3EButtonTonal>
                  </HStack>
                  <HStack className="space-x-3">
                    <M3EButtonTonal size="small" icon="edit">
                      编辑
                    </M3EButtonTonal>
                    <M3EButtonTonal
                      size="medium"
                      icon="share"
                      iconPosition="trailing"
                    >
                      分享
                    </M3EButtonTonal>
                    <M3EButtonTonal size="large" icon="favorite">
                      收藏
                    </M3EButtonTonal>
                  </HStack>
                </VStack>
              ) : (
                <HStack className="space-x-3 flex-wrap">
                  <M3EButtonTonal>编辑</M3EButtonTonal>
                  <M3EButtonTonal icon="favorite">收藏</M3EButtonTonal>
                  <M3EButtonTonal icon="share" iconPosition="trailing">
                    分享
                  </M3EButtonTonal>
                  {showStates && (
                    <>
                      <M3EButtonTonal disabled>禁用</M3EButtonTonal>
                      <M3EButtonTonal loading={isLoading}>
                        加载中
                      </M3EButtonTonal>
                    </>
                  )}
                </HStack>
              )}
            </VStack>

            {/* M3EButtonOutlined - 中等重要性 */}
            <VStack className="space-y-3">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                M3EButtonOutlined - 中等重要性操作
              </M3LabelSmall>

              {showSizes ? (
                <VStack className="space-y-3">
                  <HStack className="space-x-3">
                    <M3EButtonOutlined size="small">Small</M3EButtonOutlined>
                    <M3EButtonOutlined size="medium">Medium</M3EButtonOutlined>
                    <M3EButtonOutlined size="large">Large</M3EButtonOutlined>
                  </HStack>
                  <HStack className="space-x-3">
                    <M3EButtonOutlined size="small" icon="add">
                      添加
                    </M3EButtonOutlined>
                    <M3EButtonOutlined
                      size="medium"
                      icon="download"
                      iconPosition="trailing"
                    >
                      下载
                    </M3EButtonOutlined>
                    <M3EButtonOutlined size="large" icon="settings">
                      设置
                    </M3EButtonOutlined>
                  </HStack>
                </VStack>
              ) : (
                <HStack className="space-x-3 flex-wrap">
                  <M3EButtonOutlined>取消</M3EButtonOutlined>
                  <M3EButtonOutlined icon="add">添加</M3EButtonOutlined>
                  <M3EButtonOutlined icon="download" iconPosition="trailing">
                    下载
                  </M3EButtonOutlined>
                  {showStates && (
                    <>
                      <M3EButtonOutlined disabled>禁用</M3EButtonOutlined>
                      <M3EButtonOutlined loading={isLoading}>
                        加载中
                      </M3EButtonOutlined>
                    </>
                  )}
                </HStack>
              )}
            </VStack>

            {/* M3EButtonText - 低重要性 */}
            <VStack className="space-y-3">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                M3EButtonText - 低重要性操作
              </M3LabelSmall>

              {showSizes ? (
                <VStack className="space-y-3">
                  <HStack className="space-x-3">
                    <M3EButtonText size="small">Small</M3EButtonText>
                    <M3EButtonText size="medium">Medium</M3EButtonText>
                    <M3EButtonText size="large">Large</M3EButtonText>
                  </HStack>
                  <HStack className="space-x-3">
                    <M3EButtonText size="small" icon="info">
                      了解更多
                    </M3EButtonText>
                    <M3EButtonText
                      size="medium"
                      icon="help"
                      iconPosition="trailing"
                    >
                      帮助
                    </M3EButtonText>
                    <M3EButtonText size="large" icon="chevron_right">
                      查看详情
                    </M3EButtonText>
                  </HStack>
                </VStack>
              ) : (
                <HStack className="space-x-3 flex-wrap">
                  <M3EButtonText>跳过</M3EButtonText>
                  <M3EButtonText icon="info">了解更多</M3EButtonText>
                  <M3EButtonText icon="help" iconPosition="trailing">
                    帮助
                  </M3EButtonText>
                  {showStates && (
                    <>
                      <M3EButtonText disabled>禁用</M3EButtonText>
                      <M3EButtonText loading={isLoading}>加载中</M3EButtonText>
                    </>
                  )}
                </HStack>
              )}
            </VStack>

            {/* M3EButtonElevated - 需要分离的操作 */}
            <VStack className="space-y-3">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                M3EButtonElevated - 需要视觉分离的操作
              </M3LabelSmall>

              {showSizes ? (
                <VStack className="space-y-3">
                  <HStack className="space-x-3">
                    <M3EButtonElevated size="small">Small</M3EButtonElevated>
                    <M3EButtonElevated size="medium">Medium</M3EButtonElevated>
                    <M3EButtonElevated size="large">Large</M3EButtonElevated>
                  </HStack>
                  <HStack className="space-x-3">
                    <M3EButtonElevated size="small" icon="open_in_new">
                      打开
                    </M3EButtonElevated>
                    <M3EButtonElevated
                      size="medium"
                      icon="launch"
                      iconPosition="trailing"
                    >
                      启动
                    </M3EButtonElevated>
                    <M3EButtonElevated size="large" icon="play_arrow">
                      播放
                    </M3EButtonElevated>
                  </HStack>
                </VStack>
              ) : (
                <HStack className="space-x-3 flex-wrap">
                  <M3EButtonElevated>提升</M3EButtonElevated>
                  <M3EButtonElevated icon="open_in_new">打开</M3EButtonElevated>
                  <M3EButtonElevated icon="launch" iconPosition="trailing">
                    启动
                  </M3EButtonElevated>
                  {showStates && (
                    <>
                      <M3EButtonElevated disabled>禁用</M3EButtonElevated>
                      <M3EButtonElevated loading={isLoading}>
                        加载中
                      </M3EButtonElevated>
                    </>
                  )}
                </HStack>
              )}
            </VStack>
          </VStack>

          {/* 全宽按钮示例 */}
          <VStack className="space-y-4">
            <M3LabelLarge
              className={`${
                isDark ? 'text-on-surface-dark' : 'text-on-surface-light'
              }`}
            >
              全宽按钮
            </M3LabelLarge>

            <VStack className="space-y-3">
              <M3EButtonFilled fullWidth icon="check">
                确认并继续
              </M3EButtonFilled>
              <M3EButtonTonal fullWidth icon="edit">
                编辑资料
              </M3EButtonTonal>
              <M3EButtonOutlined fullWidth>取消操作</M3EButtonOutlined>
            </VStack>
          </VStack>

          {/* M3E 特性说明 */}
          <VStack className="space-y-4">
            <M3LabelLarge
              className={`${
                isDark ? 'text-on-surface-dark' : 'text-on-surface-light'
              }`}
            >
              M3E 命名规范特性
            </M3LabelLarge>

            <VStack className="space-y-2">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                🏷️ 新的M3E前缀命名规范，更加清晰明确
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                📚 完整的JSDoc文档支持
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                🔄 向后兼容的deprecated导出
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                ✨ 符合Material Design 3 Expressive规范
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                🎨 精确的M3E色彩系统
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                💫 优化的状态层和微交互
              </M3LabelSmall>
            </VStack>
          </VStack>

          {/* 使用指南 */}
          <VStack className="space-y-4">
            <M3LabelLarge
              className={`${
                isDark ? 'text-on-surface-dark' : 'text-on-surface-light'
              }`}
            >
              M3E 组件使用指南
            </M3LabelLarge>

            <VStack className="space-y-2">
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                • M3EButtonFilled: 用于最重要的主要操作
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                • M3EButtonTonal: 用于次要但重要的操作
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                • M3EButtonOutlined: 用于中等重要性的操作
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                • M3EButtonText: 用于低重要性或辅助操作
              </M3LabelSmall>
              <M3LabelSmall
                className={`${
                  isDark
                    ? 'text-on-surface-variant-dark'
                    : 'text-on-surface-variant-light'
                }`}
              >
                • M3EButtonElevated: 需要视觉分离或在复杂背景上使用
              </M3LabelSmall>
            </VStack>
          </VStack>

          {/* 底部间距 */}
          <View style={{ height: 40 }} />
        </VStack>
      </ScrollView>
    </SafeAreaView>
  );
};

export default M3EButtonsTestScreen;
